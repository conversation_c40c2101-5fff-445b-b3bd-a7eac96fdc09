"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const globals_1 = require("@jest/globals");
const search_service_1 = require("../../src/search/search.service");
const events_entity_1 = require("../../src/events/events.entity");
const venue_entity_1 = require("../../src/venues/entities/venue.entity");
const talent_entity_1 = require("../../src/talent/talent.entity");
const spotlight_entity_1 = require("../../src/spotlight/spotlight.entity");
const typeorm_1 = require("@nestjs/typeorm");
const test_helpers_1 = require("../utils/test-helpers");
(0, globals_1.describe)('SearchService - Featured Spotlights Integration', () => {
    let service;
    let eventRepository;
    let venueRepository;
    let talentRepository;
    let spotlightRepository;
    let mockVenueQueryBuilder;
    let mockTalentQueryBuilder;
    let mockEventQueryBuilder;
    (0, globals_1.beforeEach)(async () => {
        eventRepository = (0, test_helpers_1.createMockRepository)();
        venueRepository = (0, test_helpers_1.createMockRepository)();
        talentRepository = (0, test_helpers_1.createMockRepository)();
        spotlightRepository = (0, test_helpers_1.createMockRepository)();
        mockVenueQueryBuilder = {
            select: globals_1.jest.fn().mockReturnThis(),
            leftJoin: globals_1.jest.fn().mockReturnThis(),
            where: globals_1.jest.fn().mockReturnThis(),
            andWhere: globals_1.jest.fn().mockReturnThis(),
            orderBy: globals_1.jest.fn().mockReturnThis(),
            addOrderBy: globals_1.jest.fn().mockReturnThis(),
            skip: globals_1.jest.fn().mockReturnThis(),
            take: globals_1.jest.fn().mockReturnThis(),
            getQuery: globals_1.jest.fn().mockReturnValue('SELECT * FROM venue'),
            getParameters: globals_1.jest.fn().mockReturnValue({}),
            getRawMany: globals_1.jest.fn().mockResolvedValue([
                { id: 'venue-1' },
                { id: 'venue-2' },
                { id: 'venue-3' }
            ])
        };
        mockTalentQueryBuilder = {
            select: globals_1.jest.fn().mockReturnThis(),
            leftJoin: globals_1.jest.fn().mockReturnThis(),
            where: globals_1.jest.fn().mockReturnThis(),
            andWhere: globals_1.jest.fn().mockReturnThis(),
            orderBy: globals_1.jest.fn().mockReturnThis(),
            addOrderBy: globals_1.jest.fn().mockReturnThis(),
            skip: globals_1.jest.fn().mockReturnThis(),
            take: globals_1.jest.fn().mockReturnThis(),
            getQuery: globals_1.jest.fn().mockReturnValue('SELECT * FROM talent'),
            getParameters: globals_1.jest.fn().mockReturnValue({}),
            getRawMany: globals_1.jest.fn().mockResolvedValue([
                { id: 1 },
                { id: 2 },
                { id: 3 }
            ])
        };
        mockEventQueryBuilder = {
            leftJoinAndSelect: globals_1.jest.fn().mockReturnThis(),
            where: globals_1.jest.fn().mockReturnThis(),
            andWhere: globals_1.jest.fn().mockReturnThis(),
            orderBy: globals_1.jest.fn().mockReturnThis(),
            getMany: globals_1.jest.fn().mockResolvedValue([])
        };
        venueRepository.createQueryBuilder.mockReturnValue(mockVenueQueryBuilder);
        talentRepository.createQueryBuilder.mockReturnValue(mockTalentQueryBuilder);
        eventRepository.createQueryBuilder.mockReturnValue(mockEventQueryBuilder);
        venueRepository.find.mockResolvedValue([
            { id: 'venue-1', name: 'Featured Venue', featured: true },
            { id: 'venue-2', name: 'Spotlight Venue', featured: false },
            { id: 'venue-3', name: 'Another Venue', featured: false }
        ]);
        talentRepository.find.mockResolvedValue([
            { id: 1, name: 'Featured Talent', featured: true },
            { id: 2, name: 'Spotlight Talent', featured: false },
            { id: 3, name: 'Another Talent', featured: false }
        ]);
        const module = await testing_1.Test.createTestingModule({
            providers: [
                search_service_1.SearchService,
                {
                    provide: (0, typeorm_1.getRepositoryToken)(events_entity_1.Event),
                    useValue: eventRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(venue_entity_1.Venue),
                    useValue: venueRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(talent_entity_1.Talent),
                    useValue: talentRepository,
                },
                {
                    provide: (0, typeorm_1.getRepositoryToken)(spotlight_entity_1.Spotlight),
                    useValue: spotlightRepository,
                },
            ],
        }).compile();
        service = module.get(search_service_1.SearchService);
    });
    (0, globals_1.it)('should be defined', () => {
        (0, globals_1.expect)(service).toBeDefined();
    });
    (0, globals_1.describe)('search with featured spotlights', () => {
        (0, globals_1.it)('should include the spotlight join for venues when featuredOnly is true', async () => {
            await service.search('test', undefined, undefined, undefined, true);
            (0, globals_1.expect)(mockVenueQueryBuilder.leftJoin).toHaveBeenCalledWith('spotlight', 'spotlight', 'spotlight.venueId = venue.id');
            (0, globals_1.expect)(mockVenueQueryBuilder.andWhere).toHaveBeenCalledWith('(venue.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)', { featuredThreshold: 0 });
        });
        (0, globals_1.it)('should include the spotlight join for talents when featuredOnly is true', async () => {
            await service.search('test', undefined, undefined, undefined, true);
            (0, globals_1.expect)(mockTalentQueryBuilder.leftJoin).toHaveBeenCalledWith('spotlight', 'spotlight', 'spotlight.talentId = talent.id');
            (0, globals_1.expect)(mockTalentQueryBuilder.andWhere).toHaveBeenCalledWith('(talent.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)', { featuredThreshold: 0 });
        });
        (0, globals_1.it)('should NOT filter events by featured status when featuredOnly is true (events show all within date limits)', async () => {
            await service.search('test', undefined, undefined, undefined, true);
            (0, globals_1.expect)(mockEventQueryBuilder.andWhere).not.toHaveBeenCalledWith('event.featured = :featured', { featured: true });
            (0, globals_1.expect)(mockEventQueryBuilder.andWhere).toHaveBeenCalledWith('event.startDateTime >= :now', { now: globals_1.expect.any(Date) });
        });
        (0, globals_1.it)('should not filter by featured status when featuredOnly is false', async () => {
            await service.search('test', undefined, undefined, undefined, false);
            const venueAndWhereCalls = mockVenueQueryBuilder.andWhere.mock.calls.map(call => call[0]);
            (0, globals_1.expect)(venueAndWhereCalls).not.toContain('(venue.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)');
            const talentAndWhereCalls = mockTalentQueryBuilder.andWhere.mock.calls.map(call => call[0]);
            (0, globals_1.expect)(talentAndWhereCalls).not.toContain('(talent.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)');
            const eventAndWhereCalls = mockEventQueryBuilder.andWhere.mock.calls.map(call => call[0]);
            (0, globals_1.expect)(eventAndWhereCalls).not.toContain('event.featured = :featured');
        });
        (0, globals_1.it)('should use the same query pattern as talent and venue services', async () => {
            await service.search('test', undefined, undefined, undefined, true);
            (0, globals_1.expect)(mockVenueQueryBuilder.select).toHaveBeenCalledWith('DISTINCT venue.id', 'id');
            (0, globals_1.expect)(venueRepository.find).toHaveBeenCalled();
            (0, globals_1.expect)(mockTalentQueryBuilder.select).toHaveBeenCalledWith('DISTINCT talent.id', 'id');
            (0, globals_1.expect)(talentRepository.find).toHaveBeenCalled();
        });
    });
});
//# sourceMappingURL=search-featured-spotlight.spec.js.map