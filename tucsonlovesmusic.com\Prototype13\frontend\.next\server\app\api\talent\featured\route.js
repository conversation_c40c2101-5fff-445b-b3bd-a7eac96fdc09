/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/talent/featured/route";
exports.ids = ["app/api/talent/featured/route"];
exports.modules = {

/***/ "(rsc)/./app/api/talent/featured/route.ts":
/*!******************************************!*\
  !*** ./app/api/talent/featured/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./lib/config.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n\n\n\n\nconst dynamic = 'force-dynamic';\nconst revalidate = 0;\nasync function GET(request) {\n    const headersList = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const host = headersList.get('host') || '';\n    const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_2__.getServerApiUrl)(host);\n    const token = request.headers.get('Authorization');\n    try {\n        const response = await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_3__.fetchWithRetry)(`${apiUrl}/talent/featured`, {\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': token || ''\n            },\n            cache: 'no-store'\n        });\n        if (!response.ok) {\n            throw new Error('Failed to fetch featured talent');\n        }\n        const talent = await response.json();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(talent);\n    } catch (error) {\n        console.error('Error in featured talent route:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch featured talent'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/talent/featured/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithRetry: () => (/* binding */ fetchWithRetry),\n/* harmony export */   safeJsonFetch: () => (/* binding */ safeJsonFetch)\n/* harmony export */ });\nasync function fetchWithRetry(url, options = {}, maxRetries = 3, baseDelay = 1000) {\n    let retries = 0;\n    const attempt = async ()=>{\n        try {\n            const response = await fetch(url, options);\n            if (response.status === 429 && retries < maxRetries) {\n                // Exponential backoff for rate limiting\n                const delay = baseDelay * Math.pow(2, retries);\n                retries++;\n                console.warn(`Rate limited. Retrying in ${delay}ms (Attempt ${retries})`);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                return attempt();\n            }\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return response;\n        } catch (error) {\n            if (retries < maxRetries) {\n                const delay = baseDelay * Math.pow(2, retries);\n                retries++;\n                console.warn(`Fetch error. Retrying in ${delay}ms (Attempt ${retries})`, error);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                return attempt();\n            }\n            throw error;\n        }\n    };\n    return attempt();\n}\nasync function safeJsonFetch(url, options = {}, maxRetries = 3) {\n    const response = await fetchWithRetry(url, options, maxRetries);\n    return response.json();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/config.ts":
/*!***********************!*\
  !*** ./lib/config.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getServerApiUrl: () => (/* binding */ getServerApiUrl)\n/* harmony export */ });\n// Debug environment variables - removed for production\nif (true) {}\n// Define the correct API URLs (allow explicit override via env)\nconst API_URLS = {\n    development: process.env.NEXT_PUBLIC_API_BASE_URL || ( true ? 'http://localhost:4000' : 0),\n    production: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://tucsonlovesmusic-api-aad3484ec0df.herokuapp.com',\n    local: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000'\n};\n// Auth0 configuration\nconst getAuth0Config = ()=>{\n    // Hardcode tenant configuration to avoid env drift in deploys\n    return {\n        domain: 'dev-q8s86rgn7vmcvrco.us.auth0.com',\n        clientId: 'tbboBdkgReB764L9HtHav4VADfsu6txM',\n        audience: 'https://tucsonlovesmusic.com/api',\n        scope: 'openid profile email read:admin write:admin offline_access read:events read:venues write:venues venue:access'\n    };\n};\n// Stripe configuration\nconst getStripeConfig = ()=>{\n    const publishableKey = \"pk_test_51QT8OhRxBJaRlFvtGBl4Q2iOaQBRTdAWQb5bKbbxf7nYzJ6OnanRqiU2hdVcTfB6hVfV1GeuhE6HCZRIPDvvYGu000oGj27BdQ\";\n    if (!publishableKey) {\n        throw new Error('Missing required Stripe configuration');\n    }\n    return {\n        publishableKey\n    };\n};\n// Determine current environment\nconst getEnvironment = ()=>{\n    const env = \"local\" || 0 || 0;\n    return env === 'production' ? 'production' : 'development';\n};\n// Configuration object\nconst config = {\n    apiBaseUrl: API_URLS[getEnvironment()],\n    deployEnv: getEnvironment(),\n    auth0: getAuth0Config(),\n    stripe: getStripeConfig()\n};\n// Server-side API URL function\nconst getServerApiUrl = (hostname)=>{\n    // If hostname is provided, use it to determine the environment\n    if (hostname) {\n        if (hostname.includes('dev-tucsonlovesmusic.netlify.app')) {\n            return API_URLS.development;\n        }\n        if (hostname.includes('tucsonlovesmusic.netlify.app') && !hostname.includes('dev-')) {\n            return API_URLS.production;\n        }\n    }\n    // Default to local or development\n    return config.apiBaseUrl;\n};\n// Client-side API URL function\nconst getApiUrl = ()=>{\n    // Check if we're in a browser environment\n    if (false) {}\n    // Default to environment-based configuration\n    const deployEnv = \"local\" || 0;\n    return deployEnv === 'production' ? API_URLS.production : API_URLS.development;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,******************************************************************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\n//# sourceURL=webpack-internal:///(rsc)/./lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftalent%2Ffeatured%2Froute&page=%2Fapi%2Ftalent%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftalent%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftalent%2Ffeatured%2Froute&page=%2Fapi%2Ftalent%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftalent%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_roxas_OneDrive_Desktop_PROJECTS_tucsonlovesmusic_com_Prototype13_frontend_app_api_talent_featured_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/talent/featured/route.ts */ \"(rsc)/./app/api/talent/featured/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/talent/featured/route\",\n        pathname: \"/api/talent/featured\",\n        filename: \"route\",\n        bundlePath: \"app/api/talent/featured/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\app\\\\api\\\\talent\\\\featured\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_roxas_OneDrive_Desktop_PROJECTS_tucsonlovesmusic_com_Prototype13_frontend_app_api_talent_featured_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftalent%2Ffeatured%2Froute&page=%2Fapi%2Ftalent%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftalent%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftalent%2Ffeatured%2Froute&page=%2Fapi%2Ftalent%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftalent%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();