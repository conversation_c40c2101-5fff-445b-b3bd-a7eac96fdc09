/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/venues/featured/route";
exports.ids = ["app/api/venues/featured/route"];
exports.modules = {

/***/ "(rsc)/./app/api/venues/featured/route.ts":
/*!******************************************!*\
  !*** ./app/api/venues/featured/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   revalidate: () => (/* binding */ revalidate)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/config */ \"(rsc)/./lib/config.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./lib/api-utils.ts\");\n\n\n\n\nconst dynamic = 'force-dynamic';\nconst revalidate = 0;\n// Process venue data to ensure proper formatting\nconst processVenueData = (venue)=>{\n    // Debug log for Coyote Grill venue\n    if (venue.name?.includes('Coyote Grill') || venue.slug?.includes('coyote-grill')) {\n        console.log('Processing Coyote Grill venue in API route:', {\n            id: venue.id,\n            name: venue.name,\n            imageUrl: venue.imageUrl,\n            image_url: venue.image_url,\n            imageurl: venue.imageurl,\n            slug: venue.slug\n        });\n    }\n    // Normalize image URLs - this is critical for image persistence during filtering\n    let imageUrl = null;\n    // Check all possible image URL properties\n    if (venue.imageUrl) {\n        imageUrl = venue.imageUrl;\n    } else if (venue.image_url) {\n        imageUrl = venue.image_url;\n    } else if (venue.imageurl) {\n        imageUrl = venue.imageurl;\n    }\n    // Debug log for Coyote Grill venue after normalization\n    if (venue.name?.includes('Coyote Grill') || venue.slug?.includes('coyote-grill')) {\n        console.log('Coyote Grill venue after normalization:', {\n            id: venue.id,\n            name: venue.name,\n            normalizedImageUrl: imageUrl\n        });\n    }\n    // Normalize amenities data\n    let amenities = venue.amenities;\n    // Handle different formats of amenities data\n    if (amenities === 'empty' || !amenities) {\n        // Handle explicitly marked empty values or null/undefined\n        amenities = [];\n    } else if (typeof amenities === 'string' && amenities.startsWith('{') && amenities.endsWith('}')) {\n        // Handle PostgreSQL array format: convert \"{value1,value2}\" to array\n        amenities = amenities.slice(1, -1).split(',').filter((a)=>a.trim() !== '');\n    } else if (!Array.isArray(amenities) && amenities) {\n        // If it's not an array but exists, convert to array\n        amenities = String(amenities).split(',').filter((a)=>a.trim() !== '');\n    }\n    // Create a new venue object with normalized properties\n    const normalizedVenue = {\n        ...venue,\n        // Set both imageUrl and image_url for compatibility with different components\n        imageUrl: imageUrl,\n        image_url: imageUrl,\n        amenities: amenities,\n        additionalPhotos: Array.isArray(venue.additionalPhotos) ? venue.additionalPhotos : []\n    };\n    return normalizedVenue;\n};\nasync function GET(request) {\n    const headersList = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)();\n    const host = headersList.get('host') || '';\n    const apiUrl = (0,_lib_config__WEBPACK_IMPORTED_MODULE_2__.getServerApiUrl)(host);\n    const token = request.headers.get('Authorization');\n    try {\n        const response = await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_3__.fetchWithRetry)(`${apiUrl}/venues?featured=true`, {\n            headers: {\n                'Content-Type': 'application/json',\n                'Authorization': token || ''\n            },\n            cache: 'no-store'\n        });\n        if (!response.ok) {\n            console.error('Failed to fetch venues:', response.status, response.statusText);\n            throw new Error('Failed to fetch featured venues');\n        }\n        const rawVenues = await response.json();\n        // Extract venues array from response - handle both array and object formats\n        const venuesArray = Array.isArray(rawVenues) ? rawVenues : rawVenues.venues ? rawVenues.venues : [];\n        console.log('Raw venues data structure:', {\n            isArray: Array.isArray(rawVenues),\n            hasVenuesProperty: rawVenues && typeof rawVenues === 'object' && 'venues' in rawVenues,\n            extractedArrayLength: venuesArray.length\n        });\n        // Debug log raw venue data\n        console.log('Raw venues data before processing:', venuesArray.slice(0, 3).map((v)=>({\n                id: v.id,\n                name: v.name,\n                imageUrl: v.imageUrl,\n                image_url: v.image_url,\n                imageurl: v.imageurl // Some SQL queries return lowercase column names\n            })));\n        // Process each venue to ensure proper data format\n        const venues = venuesArray.map(processVenueData);\n        // Debug log processed venue data\n        console.log('Processed venues data:', venues.map((v)=>({\n                id: v.id,\n                name: v.name,\n                imageUrl: v.imageUrl,\n                image_url: v.image_url\n            })));\n        // Randomize venues order for fair featured placement (no more featuredOrder)\n        const shuffledVenues = venues.sort(()=>Math.random() - 0.5);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(shuffledVenues);\n    } catch (error) {\n        console.error('Error in featured venues route:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch featured venues'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/venues/featured/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/api-utils.ts":
/*!**************************!*\
  !*** ./lib/api-utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithRetry: () => (/* binding */ fetchWithRetry),\n/* harmony export */   safeJsonFetch: () => (/* binding */ safeJsonFetch)\n/* harmony export */ });\nasync function fetchWithRetry(url, options = {}, maxRetries = 3, baseDelay = 1000) {\n    let retries = 0;\n    const attempt = async ()=>{\n        try {\n            const response = await fetch(url, options);\n            if (response.status === 429 && retries < maxRetries) {\n                // Exponential backoff for rate limiting\n                const delay = baseDelay * Math.pow(2, retries);\n                retries++;\n                console.warn(`Rate limited. Retrying in ${delay}ms (Attempt ${retries})`);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                return attempt();\n            }\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            return response;\n        } catch (error) {\n            if (retries < maxRetries) {\n                const delay = baseDelay * Math.pow(2, retries);\n                retries++;\n                console.warn(`Fetch error. Retrying in ${delay}ms (Attempt ${retries})`, error);\n                await new Promise((resolve)=>setTimeout(resolve, delay));\n                return attempt();\n            }\n            throw error;\n        }\n    };\n    return attempt();\n}\nasync function safeJsonFetch(url, options = {}, maxRetries = 3) {\n    const response = await fetchWithRetry(url, options, maxRetries);\n    return response.json();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./lib/config.ts":
/*!***********************!*\
  !*** ./lib/config.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   getApiUrl: () => (/* binding */ getApiUrl),\n/* harmony export */   getServerApiUrl: () => (/* binding */ getServerApiUrl)\n/* harmony export */ });\n// Debug environment variables - removed for production\nif (true) {}\n// Define the correct API URLs (allow explicit override via env)\nconst API_URLS = {\n    development: process.env.NEXT_PUBLIC_API_BASE_URL || ( true ? 'http://localhost:4000' : 0),\n    production: process.env.NEXT_PUBLIC_API_BASE_URL || 'https://tucsonlovesmusic-api-aad3484ec0df.herokuapp.com',\n    local: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:4000'\n};\n// Auth0 configuration\nconst getAuth0Config = ()=>{\n    // Hardcode tenant configuration to avoid env drift in deploys\n    return {\n        domain: 'dev-q8s86rgn7vmcvrco.us.auth0.com',\n        clientId: 'tbboBdkgReB764L9HtHav4VADfsu6txM',\n        audience: 'https://tucsonlovesmusic.com/api',\n        scope: 'openid profile email read:admin write:admin offline_access read:events read:venues write:venues venue:access'\n    };\n};\n// Stripe configuration\nconst getStripeConfig = ()=>{\n    const publishableKey = \"pk_test_51QT8OhRxBJaRlFvtGBl4Q2iOaQBRTdAWQb5bKbbxf7nYzJ6OnanRqiU2hdVcTfB6hVfV1GeuhE6HCZRIPDvvYGu000oGj27BdQ\";\n    if (!publishableKey) {\n        throw new Error('Missing required Stripe configuration');\n    }\n    return {\n        publishableKey\n    };\n};\n// Determine current environment\nconst getEnvironment = ()=>{\n    const env = \"local\" || 0 || 0;\n    return env === 'production' ? 'production' : 'development';\n};\n// Configuration object\nconst config = {\n    apiBaseUrl: API_URLS[getEnvironment()],\n    deployEnv: getEnvironment(),\n    auth0: getAuth0Config(),\n    stripe: getStripeConfig()\n};\n// Server-side API URL function\nconst getServerApiUrl = (hostname)=>{\n    // If hostname is provided, use it to determine the environment\n    if (hostname) {\n        if (hostname.includes('dev-tucsonlovesmusic.netlify.app')) {\n            return API_URLS.development;\n        }\n        if (hostname.includes('tucsonlovesmusic.netlify.app') && !hostname.includes('dev-')) {\n            return API_URLS.production;\n        }\n    }\n    // Default to local or development\n    return config.apiBaseUrl;\n};\n// Client-side API URL function\nconst getApiUrl = ()=>{\n    // Check if we're in a browser environment\n    if (false) {}\n    // Default to environment-based configuration\n    const deployEnv = \"local\" || 0;\n    return deployEnv === 'production' ? API_URLS.production : API_URLS.development;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,******************************************************************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\n//# sourceURL=webpack-internal:///(rsc)/./lib/config.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvenues%2Ffeatured%2Froute&page=%2Fapi%2Fvenues%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvenues%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvenues%2Ffeatured%2Froute&page=%2Fapi%2Fvenues%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvenues%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_roxas_OneDrive_Desktop_PROJECTS_tucsonlovesmusic_com_Prototype13_frontend_app_api_venues_featured_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/venues/featured/route.ts */ \"(rsc)/./app/api/venues/featured/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/venues/featured/route\",\n        pathname: \"/api/venues/featured\",\n        filename: \"route\",\n        bundlePath: \"app/api/venues/featured/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\app\\\\api\\\\venues\\\\featured\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_roxas_OneDrive_Desktop_PROJECTS_tucsonlovesmusic_com_Prototype13_frontend_app_api_venues_featured_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvenues%2Ffeatured%2Froute&page=%2Fapi%2Fvenues%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvenues%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fvenues%2Ffeatured%2Froute&page=%2Fapi%2Fvenues%2Ffeatured%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvenues%2Ffeatured%2Froute.ts&appDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Croxas%5COneDrive%5CDesktop%5CPROJECTS%5Ctucsonlovesmusic.com%5CPrototype13%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();