{"version": 3, "file": "search-featured-spotlight.spec.js", "sourceRoot": "", "sources": ["../../../__tests__/services/search-featured-spotlight.spec.ts"], "names": [], "mappings": ";;AACA,6CAAsD;AACtD,2CAAuE;AACvE,oEAAgE;AAEhE,kEAAuD;AACvD,yEAA+D;AAC/D,kEAAwD;AACxD,2EAAiE;AAEjE,6CAAqD;AACrD,wDAA6D;AAE7D,IAAA,kBAAQ,EAAC,iDAAiD,EAAE,GAAG,EAAE;IAC/D,IAAI,OAAsB,CAAC;IAC3B,IAAI,eAA2C,CAAC;IAChD,IAAI,eAA2C,CAAC;IAChD,IAAI,gBAA6C,CAAC;IAClD,IAAI,mBAAmD,CAAC;IACxD,IAAI,qBAA0B,CAAC;IAC/B,IAAI,sBAA2B,CAAC;IAChC,IAAI,qBAA0B,CAAC;IAE/B,IAAA,oBAAU,EAAC,KAAK,IAAI,EAAE;QAEpB,eAAe,GAAG,IAAA,mCAAoB,GAAS,CAAC;QAChD,eAAe,GAAG,IAAA,mCAAoB,GAAS,CAAC;QAChD,gBAAgB,GAAG,IAAA,mCAAoB,GAAU,CAAC;QAClD,mBAAmB,GAAG,IAAA,mCAAoB,GAAa,CAAC;QAGxD,qBAAqB,GAAG;YACtB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAClC,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACpC,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACjC,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACpC,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACnC,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACtC,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAChC,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAChC,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,qBAAqB,CAAC;YAC1D,aAAa,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5C,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;gBACtC,EAAE,EAAE,EAAE,SAAS,EAAE;gBACjB,EAAE,EAAE,EAAE,SAAS,EAAE;gBACjB,EAAE,EAAE,EAAE,SAAS,EAAE;aAClB,CAAC;SACH,CAAC;QAGF,sBAAsB,GAAG;YACvB,MAAM,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAClC,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACpC,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACjC,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACpC,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACnC,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACtC,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAChC,IAAI,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAChC,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,sBAAsB,CAAC;YAC3D,aAAa,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,eAAe,CAAC,EAAE,CAAC;YAC5C,UAAU,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC;gBACtC,EAAE,EAAE,EAAE,CAAC,EAAE;gBACT,EAAE,EAAE,EAAE,CAAC,EAAE;gBACT,EAAE,EAAE,EAAE,CAAC,EAAE;aACV,CAAC;SACH,CAAC;QAGF,qBAAqB,GAAG;YACtB,iBAAiB,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YAC7C,KAAK,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACjC,QAAQ,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACpC,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,cAAc,EAAE;YACnC,OAAO,EAAE,cAAI,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,EAAE,CAAC;SACzC,CAAC;QAGD,eAAe,CAAC,kBAAgC,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;QACxF,gBAAgB,CAAC,kBAAgC,CAAC,eAAe,CAAC,sBAAsB,CAAC,CAAC;QAC1F,eAAe,CAAC,kBAAgC,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;QAGxF,eAAe,CAAC,IAAkB,CAAC,iBAAiB,CAAC;YACpD,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,IAAI,EAAE;YACzD,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE;YAC3D,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAE,QAAQ,EAAE,KAAK,EAAE;SAC1D,CAAC,CAAC;QAEF,gBAAgB,CAAC,IAAkB,CAAC,iBAAiB,CAAC;YACrD,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,QAAQ,EAAE,IAAI,EAAE;YAClD,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,QAAQ,EAAE,KAAK,EAAE;YACpD,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE;SACnD,CAAC,CAAC;QAGH,MAAM,MAAM,GAAkB,MAAM,cAAI,CAAC,mBAAmB,CAAC;YAC3D,SAAS,EAAE;gBACT,8BAAa;gBACb;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,qBAAK,CAAC;oBAClC,QAAQ,EAAE,eAAe;iBAC1B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,oBAAK,CAAC;oBAClC,QAAQ,EAAE,eAAe;iBAC1B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,sBAAM,CAAC;oBACnC,QAAQ,EAAE,gBAAgB;iBAC3B;gBACD;oBACE,OAAO,EAAE,IAAA,4BAAkB,EAAC,4BAAS,CAAC;oBACtC,QAAQ,EAAE,mBAAmB;iBAC9B;aACF;SACF,CAAC,CAAC,OAAO,EAAE,CAAC;QAGb,OAAO,GAAG,MAAM,CAAC,GAAG,CAAgB,8BAAa,CAAC,CAAC;IACrD,CAAC,CAAC,CAAC;IAEH,IAAA,YAAE,EAAC,mBAAmB,EAAE,GAAG,EAAE;QAC3B,IAAA,gBAAM,EAAC,OAAO,CAAC,CAAC,WAAW,EAAE,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,IAAA,kBAAQ,EAAC,iCAAiC,EAAE,GAAG,EAAE;QAC/C,IAAA,YAAE,EAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;YAEtF,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAIpE,IAAA,gBAAM,EAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACzD,WAAW,EACX,WAAW,EACX,8BAA8B,CAC/B,CAAC;YAGF,IAAA,gBAAM,EAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACzD,kFAAkF,EAClF,EAAE,iBAAiB,EAAE,CAAC,EAAE,CACzB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,yEAAyE,EAAE,KAAK,IAAI,EAAE;YAEvF,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAIpE,IAAA,gBAAM,EAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAC1D,WAAW,EACX,WAAW,EACX,gCAAgC,CACjC,CAAC;YAGF,IAAA,gBAAM,EAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CAC1D,mFAAmF,EACnF,EAAE,iBAAiB,EAAE,CAAC,EAAE,CACzB,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,4GAA4G,EAAE,KAAK,IAAI,EAAE;YAE1H,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAIpE,IAAA,gBAAM,EAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAC7D,4BAA4B,EAC5B,EAAE,QAAQ,EAAE,IAAI,EAAE,CACnB,CAAC;YAGF,IAAA,gBAAM,EAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,oBAAoB,CACzD,6BAA6B,EAC7B,EAAE,GAAG,EAAE,gBAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAC1B,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,iEAAiE,EAAE,KAAK,IAAI,EAAE;YAE/E,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;YAIrE,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,IAAA,gBAAM,EAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,SAAS,CACtC,kFAAkF,CACnF,CAAC;YAGF,MAAM,mBAAmB,GAAG,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5F,IAAA,gBAAM,EAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,SAAS,CACvC,mFAAmF,CACpF,CAAC;YAGF,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1F,IAAA,gBAAM,EAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,4BAA4B,CAAC,CAAC;QACzE,CAAC,CAAC,CAAC;QAEH,IAAA,YAAE,EAAC,gEAAgE,EAAE,KAAK,IAAI,EAAE;YAE9E,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAIpE,IAAA,gBAAM,EAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,mBAAmB,EAAE,IAAI,CAAC,CAAC;YAGrF,IAAA,gBAAM,EAAC,eAAe,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;YAIhD,IAAA,gBAAM,EAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,oBAAoB,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;YAGvF,IAAA,gBAAM,EAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}