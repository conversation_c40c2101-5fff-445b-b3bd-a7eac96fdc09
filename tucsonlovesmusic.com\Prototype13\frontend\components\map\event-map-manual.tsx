'use client';

import { useEffect, useRef, useState, useCallback } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import "@/styles/map.css";
import { Event } from "@/types/events";
import { format } from 'date-fns';
import { Button } from "@/components/ui/button";
import { useLocation } from "@/hooks/use-location"; 
import { MapPin, Search } from "lucide-react";
import { MapDistanceSelect } from "@/components/map/map-distance-select";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

interface EventMapManualProps {
  events: Event[];
  selectedEvent?: Event | null;
  onEventSelect?: (event: Event | null) => void;
  center?: [number, number];
  zoom?: number;
  mapDistance?: string;
  userLocation?: [number, number] | null;
  onLocationSelect?: (location: [number, number]) => void;
  onReset?: () => void;
}

// Utility function to get event coordinates
function getEventCoordinates(event: Event): [number, number] | null {
  if (event.venue?.latitude && event.venue?.longitude) {
    const lat = parseFloat(event.venue.latitude);
    const lng = parseFloat(event.venue.longitude);
    if (!isNaN(lat) && !isNaN(lng)) {
      return [lat, lng];
    }
  }
  return null;
}

// Calculate distance between two points using Haversine formula
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3958.8; // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in miles
}

// Helper function to calculate appropriate zoom level for a given distance
function getZoomForDistance(miles: number): number {
  if (miles <= 1) return 15;
  if (miles <= 5) return 13;
  if (miles <= 10) return 12;
  if (miles <= 20) return 11;
  return 10;
}

export default function EventMapManual({ 
  events, 
  selectedEvent = null, 
  onEventSelect, 
  center,
  zoom: initialZoom,
  mapDistance,
  userLocation,
  onLocationSelect,
  onReset
}: EventMapManualProps) {
  const { getCurrentLocation } = useLocation();
  const defaultCenter: [number, number] = [32.2226, -110.9747];
  
  const mapRef = useRef<L.Map | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const markersRef = useRef<L.Marker[]>([]);
  const circleRef = useRef<L.Circle | null>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  // State management
  const [distance, setDistance] = useState(mapDistance || "10");
  const [locationAccessed, setLocationAccessed] = useState(false);
  const [locationModeActive, setLocationModeActive] = useState(false);
  const [userLocationState, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [filteredEvents, setFilteredEvents] = useState<Event[]>(events);
  const [searchInput, setSearchInput] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState(false);

  // Initialize map manually
  useEffect(() => {
    if (!containerRef.current || mapRef.current) return;

    try {
      const map = L.map(containerRef.current, {
        center: center || defaultCenter,
        zoom: 10,
        zoomControl: true,
        attributionControl: true
      });

      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(map);

      mapRef.current = map;
      setIsMapReady(true);

      console.log('Manual event map created successfully');
    } catch (error) {
      console.error('Failed to create manual event map:', error);
    }
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (mapRef.current) {
        try {
          mapRef.current.remove();
          mapRef.current = null;
          console.log('Manual event map cleaned up');
        } catch (error) {
          console.warn('Event map cleanup error:', error);
        }
      }
    };
  }, []);

  // Handle user location
  useEffect(() => {
    if (userLocation && mapDistance) {
      setUserLocation({ latitude: userLocation[0], longitude: userLocation[1] });
      setLocationModeActive(true);
      setDistance(mapDistance);
    }
  }, [userLocation, mapDistance]);

  // Handle events and markers
  useEffect(() => {
    if (!mapRef.current || !isMapReady) return;

    const map = mapRef.current;

    // Clear existing markers
    markersRef.current.forEach(marker => {
      map.removeLayer(marker);
    });
    markersRef.current = [];

    // Create icons
    const icon = L.icon({
      iconUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
      iconRetinaUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png",
      shadowUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
      shadowSize: [41, 41]
    });

    const selectedIcon = L.icon({
      iconUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
      iconRetinaUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png",
      shadowUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
      iconSize: [30, 46],
      iconAnchor: [15, 46],
      popupAnchor: [1, -34],
      shadowSize: [41, 41],
      className: "selected-marker"
    });

    // Determine which events to display
    const eventsToDisplay = selectedEvent 
      ? [selectedEvent] 
      : (locationModeActive && userLocationState ? filteredEvents : events);

    // Add event markers
    eventsToDisplay.forEach(event => {
      const coords = getEventCoordinates(event);
      if (coords) {
        const marker = L.marker(coords, {
          icon: selectedEvent?.id === event.id ? selectedIcon : icon
        }).addTo(map);

        const popupContent = `
          <div class="text-sm leading-tight">
            <h3 class="font-bold mb-1">${event.name}</h3>
            <div class="space-y-0">
              <div>${format(new Date(event.startDateTime), 'MMMM d, yyyy h:mm a')}</div>
              ${event.venue ? `<div>${event.venue.name}</div>` : ''}
            </div>
          </div>
        `;

        marker.bindPopup(popupContent);

        marker.on('click', () => {
          if (selectedEvent?.id === event.id) {
            onEventSelect?.(null);
          } else {
            onEventSelect?.(event);
          }
        });

        markersRef.current.push(marker);
      }
    });

    // Fit bounds or center map appropriately
    if (selectedEvent) {
      const coords = getEventCoordinates(selectedEvent);
      if (coords) {
        map.setView(coords, 15, { animate: true });
      }
    } else if (eventsToDisplay.length > 0 && markersRef.current.length > 0) {
      const group = new L.FeatureGroup(markersRef.current);
      map.fitBounds(group.getBounds(), { padding: [20, 20] });
    }

  }, [events, selectedEvent, filteredEvents, locationModeActive, userLocationState, isMapReady]);

  // Handle radius circle
  useEffect(() => {
    if (!mapRef.current || !isMapReady) return;

    const map = mapRef.current;

    // Remove existing circle
    if (circleRef.current) {
      map.removeLayer(circleRef.current);
      circleRef.current = null;
    }

    // Add radius circle if user location is active
    if (userLocationState && locationModeActive) {
      const circle = L.circle(
        [userLocationState.latitude, userLocationState.longitude], 
        {
          radius: Number(distance) * 1609.34, // Convert miles to meters
          color: '#3b82f6',
          fillColor: '#3b82f680',
          fillOpacity: 0.1,
          weight: 2
        }
      ).addTo(map);

      circleRef.current = circle;

      // Center on user location with appropriate zoom
      const zoomLevel = getZoomForDistance(Number(distance));
      map.setView([userLocationState.latitude, userLocationState.longitude], zoomLevel, { animate: true });
    }
  }, [userLocationState, distance, locationModeActive, isMapReady]);

  // Filter events based on distance
  useEffect(() => {
    if (!userLocationState || !locationModeActive) {
      setFilteredEvents(events);
      return;
    }

    const maxDistance = Number(distance);
    const filtered = events.filter(event => {
      const coords = getEventCoordinates(event);
      if (!coords) return false;
      
      const eventDistance = calculateDistance(
        userLocationState.latitude, 
        userLocationState.longitude, 
        coords[0], 
        coords[1]
      );
      
      return eventDistance <= maxDistance;
    });
    
    setFilteredEvents(filtered);
  }, [events, userLocationState, distance, locationModeActive]);

  const handleLocationToggle = async () => {
    if (locationModeActive) {
      // Reset location mode
      setLocationModeActive(false);
      setUserLocation(null);
      setLocationAccessed(false);
      setDistance('10');
      
      if (mapRef.current) {
        // Remove circle
        if (circleRef.current) {
          mapRef.current.removeLayer(circleRef.current);
          circleRef.current = null;
        }
        
        // Reset to default view
        mapRef.current.setView(defaultCenter, 11, { animate: true });
      }
      
      setFilteredEvents(events);
      onEventSelect?.(null);
      // Don't call onLocationSelect with [0,0] - let the map stay at defaultCenter
      
      return;
    }

    try {
      const location = await getCurrentLocation();
      
      if (!location) {
        console.warn('No location retrieved');
        return;
      }
      
      setUserLocation(location);
      onLocationSelect?.([location.latitude, location.longitude]);
      setLocationAccessed(true);
      setLocationModeActive(true);
      
    } catch (error) {
      console.error("Location selection error:", error);
    }
  };

  const handleLocationSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchInput.trim()) return;
    
    setIsSearching(true);
    setSearchError(false);
    
    try {
      let searchQuery = searchInput.trim();
      
      if (searchQuery.toLowerCase() === "u of a" || 
          searchQuery.toLowerCase() === "uofa") {
        searchQuery = "University of Arizona, Tucson, AZ";
      } else if (!searchQuery.toLowerCase().includes("arizona") && 
          !searchQuery.toLowerCase().includes("az")) {
        searchQuery = `${searchQuery}, Arizona`;
      }
      
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?` + 
        `format=json&` +
        `q=${encodeURIComponent(searchQuery)}&` +
        `countrycodes=us&` +
        `limit=1`
      );
      
      const data = await response.json();
      
      if (data && data.length > 0) {
        const location = {
          latitude: parseFloat(data[0].lat),
          longitude: parseFloat(data[0].lon)
        };
        
        const isInArizona = 
          location.latitude >= 31.3322 && location.latitude <= 37.0043 &&
          location.longitude >= -114.8183 && location.longitude <= -109.0452;
        
        if (!isInArizona) {
          setSearchError(true);
          setTimeout(() => setSearchError(false), 3000);
          return;
        }
        
        setUserLocation(location);
        onLocationSelect?.([location.latitude, location.longitude]);
        setLocationAccessed(true);
        setLocationModeActive(true);
        
      } else {
        setSearchError(true);
        setTimeout(() => setSearchError(false), 3000);
      }
    } catch (error) {
      console.error("Error searching location:", error);
      setSearchError(true);
      setTimeout(() => setSearchError(false), 3000);
    } finally {
      setIsSearching(false);
    }
  };

  const handleDistanceChange = (value: string) => {
    setDistance(value);
    
    if (onLocationSelect && userLocationState) {
      onLocationSelect([userLocationState.latitude, userLocationState.longitude]);
    }
  };

  // Add custom CSS for selected marker
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      .selected-marker {
        filter: hue-rotate(120deg) brightness(1.2) drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
        z-index: 1000 !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div className="w-full h-full rounded-lg overflow-hidden flex flex-col max-h-full min-h-0">
      {/* Map Controls */}
      <div className="space-y-2 mb-2 z-50 relative flex-shrink-0">
        <Button
          onClick={handleLocationToggle}
          className={cn(
            "w-full",
            locationModeActive ? "bg-primary hover:bg-primary/90" : ""
          )}
          variant={locationModeActive ? "default" : "outline"}
        >
          <MapPin className="mr-2 h-4 w-4" />
          {locationModeActive ? "Reset Map View" : "Use My Location"}
        </Button>
        
        <MapDistanceSelect 
          defaultValue={distance} 
          onChange={handleDistanceChange}
          label=""
          className="mt-2"
          disabled={!locationAccessed || !locationModeActive}
        />
        
        <form onSubmit={handleLocationSearch} className="flex w-full gap-1">
          <Input
            type="text"
            placeholder="Search in Arizona..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="h-9 text-sm"
          />
          <Button 
            type="submit" 
            size="sm" 
            variant={searchError ? "destructive" : "outline"}
            className="px-2" 
            disabled={isSearching || !searchInput.trim()}
          >
            {isSearching ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            ) : (
              <Search className="h-4 w-4" />
            )}
          </Button>
        </form>
      </div>

      {/* Map Container */}
      <div className="flex-1 min-h-[300px] max-h-full relative overflow-hidden">
        <div 
          ref={containerRef} 
          className="h-full w-full z-0"
        />
        {!isMapReady && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80">
            <div className="text-muted-foreground">Loading map...</div>
          </div>
        )}
      </div>
    </div>
  );
}
