// @ts-nocheck - Disable TypeScript checking for this test file
import { Test, TestingModule } from '@nestjs/testing';
import { jest, describe, beforeEach, it, expect } from '@jest/globals';
import { SearchService } from '../../src/search/search.service';
import { Repository } from 'typeorm';
import { Event } from '../../src/events/events.entity';
import { Venue } from '../../src/venues/entities/venue.entity';
import { Talent } from '../../src/talent/talent.entity';
import { Spotlight } from '../../src/spotlight/spotlight.entity';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { getRepositoryToken } from '@nestjs/typeorm';
import { createMockRepository } from '../utils/test-helpers';

describe('SearchService - Featured Spotlights Integration', () => {
  let service: SearchService;
  let eventRepository: Partial<Repository<Event>>;
  let venueRepository: Partial<Repository<Venue>>;
  let talentRepository: Partial<Repository<Talent>>;
  let spotlightRepository: Partial<Repository<Spotlight>>;
  let mockVenueQueryBuilder: any;
  let mockTalentQueryBuilder: any;
  let mockEventQueryBuilder: any;

  beforeEach(async () => {
    // Create mock repositories
    eventRepository = createMockRepository<Event>();
    venueRepository = createMockRepository<Venue>();
    talentRepository = createMockRepository<Talent>();
    spotlightRepository = createMockRepository<Spotlight>();

    // Create a more detailed mock query builder for venues
    mockVenueQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      addOrderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getQuery: jest.fn().mockReturnValue('SELECT * FROM venue'),
      getParameters: jest.fn().mockReturnValue({}),
      getRawMany: jest.fn().mockResolvedValue([
        { id: 'venue-1' },
        { id: 'venue-2' },
        { id: 'venue-3' }
      ])
    };

    // Create a more detailed mock query builder for talents
    mockTalentQueryBuilder = {
      select: jest.fn().mockReturnThis(),
      leftJoin: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      addOrderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getQuery: jest.fn().mockReturnValue('SELECT * FROM talent'),
      getParameters: jest.fn().mockReturnValue({}),
      getRawMany: jest.fn().mockResolvedValue([
        { id: 1 },
        { id: 2 },
        { id: 3 }
      ])
    };

    // Create a more detailed mock query builder for events
    mockEventQueryBuilder = {
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      getMany: jest.fn().mockResolvedValue([])
    };

    // Make createQueryBuilder return our mockQueryBuilder
    (venueRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockVenueQueryBuilder);
    (talentRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockTalentQueryBuilder);
    (eventRepository.createQueryBuilder as jest.Mock).mockReturnValue(mockEventQueryBuilder);

    // Mock the find method for venues and talents
    (venueRepository.find as jest.Mock).mockResolvedValue([
      { id: 'venue-1', name: 'Featured Venue', featured: true },
      { id: 'venue-2', name: 'Spotlight Venue', featured: false },
      { id: 'venue-3', name: 'Another Venue', featured: false }
    ]);

    (talentRepository.find as jest.Mock).mockResolvedValue([
      { id: 1, name: 'Featured Talent', featured: true },
      { id: 2, name: 'Spotlight Talent', featured: false },
      { id: 3, name: 'Another Talent', featured: false }
    ]);

    // Create a test module with our mocked dependencies
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SearchService,
        {
          provide: getRepositoryToken(Event),
          useValue: eventRepository,
        },
        {
          provide: getRepositoryToken(Venue),
          useValue: venueRepository,
        },
        {
          provide: getRepositoryToken(Talent),
          useValue: talentRepository,
        },
        {
          provide: getRepositoryToken(Spotlight),
          useValue: spotlightRepository,
        },
      ],
    }).compile();

    // Get the service instance
    service = module.get<SearchService>(SearchService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('search with featured spotlights', () => {
    it('should include the spotlight join for venues when featuredOnly is true', async () => {
      // Act
      await service.search('test', undefined, undefined, undefined, true);
      
      // Assert
      // Verify the leftJoin with spotlight was called for venues
      expect(mockVenueQueryBuilder.leftJoin).toHaveBeenCalledWith(
        'spotlight', 
        'spotlight', 
        'spotlight.venueId = venue.id'
      );
      
      // Verify the where condition includes both directly featured venues and those in featured spotlights
      expect(mockVenueQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(venue.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)',
        { featuredThreshold: 0 }
      );
    });

    it('should include the spotlight join for talents when featuredOnly is true', async () => {
      // Act
      await service.search('test', undefined, undefined, undefined, true);
      
      // Assert
      // Verify the leftJoin with spotlight was called for talents
      expect(mockTalentQueryBuilder.leftJoin).toHaveBeenCalledWith(
        'spotlight', 
        'spotlight', 
        'spotlight.talentId = talent.id'
      );
      
      // Verify the where condition includes both directly featured talents and those in featured spotlights
      expect(mockTalentQueryBuilder.andWhere).toHaveBeenCalledWith(
        '(talent.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)',
        { featuredThreshold: 0 }
      );
    });

    it('should NOT filter events by featured status when featuredOnly is true (events show all within date limits)', async () => {
      // Act
      await service.search('test', undefined, undefined, undefined, true);
      
      // Assert
      // Verify events are NOT filtered by featured status (new behavior)
      expect(mockEventQueryBuilder.andWhere).not.toHaveBeenCalledWith(
        'event.featured = :featured',
        { featured: true }
      );
      
      // Verify events are filtered by date (not private status anymore)
      expect(mockEventQueryBuilder.andWhere).toHaveBeenCalledWith(
        'event.startDateTime >= :now',
        { now: expect.any(Date) }
      );
    });

    it('should not filter by featured status when featuredOnly is false', async () => {
      // Act
      await service.search('test', undefined, undefined, undefined, false);
      
      // Assert
      // Verify the featured filter was not applied to venues
      const venueAndWhereCalls = mockVenueQueryBuilder.andWhere.mock.calls.map(call => call[0]);
      expect(venueAndWhereCalls).not.toContain(
        '(venue.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)'
      );

      // Verify the featured filter was not applied to talents
      const talentAndWhereCalls = mockTalentQueryBuilder.andWhere.mock.calls.map(call => call[0]);
      expect(talentAndWhereCalls).not.toContain(
        '(talent.featured > :featuredThreshold OR spotlight.featured > :featuredThreshold)'
      );
      
      // Verify the featured filter was not applied to events
      const eventAndWhereCalls = mockEventQueryBuilder.andWhere.mock.calls.map(call => call[0]);
      expect(eventAndWhereCalls).not.toContain('event.featured = :featured');
    });

    it('should use the same query pattern as talent and venue services', async () => {
      // Act
      await service.search('test', undefined, undefined, undefined, true);
      
      // Assert for venues
      // 1. First get IDs with a subquery
      expect(mockVenueQueryBuilder.select).toHaveBeenCalledWith('DISTINCT venue.id', 'id');
      
      // 2. Then fetch full data for those IDs
      expect(venueRepository.find).toHaveBeenCalled();
      
      // Assert for talents
      // 1. First get IDs with a subquery
      expect(mockTalentQueryBuilder.select).toHaveBeenCalledWith('DISTINCT talent.id', 'id');
      
      // 2. Then fetch full data for those IDs
      expect(talentRepository.find).toHaveBeenCalled();
    });
  });
});
