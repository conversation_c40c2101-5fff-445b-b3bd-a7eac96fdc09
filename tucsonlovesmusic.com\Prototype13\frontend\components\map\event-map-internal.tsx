"use client";

import { useEffect, useRef, useCallback, useState, useMemo } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>ap, Circle } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import "@/styles/map.css";
import L, { LatLngExpression, Map, PointTuple } from "leaflet";
import { Event } from "@/types/events";
import { format } from 'date-fns';
import { Button } from "@/components/ui/button";
import { useLocation } from "@/hooks/use-location"; 
import { MapPin, Search } from "lucide-react";
import { MapDistanceSelect } from "@/components/map/map-distance-select";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

// Fix Leaflet marker icon issue
const icon = L.icon({
  iconUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
  iconRetinaUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png",
  shadowUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

// Selected marker icon with different color
const selectedIcon = L.icon({
  iconUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png",
  iconRetinaUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png",
  shadowUrl: "https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png",
  iconSize: [30, 46], // Slightly larger
  iconAnchor: [15, 46],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
  className: "selected-marker" // Custom class for styling
});

// Utility function to get event coordinates
function getEventCoordinates(event: Event): [number, number] | null {
  if (event.venue?.latitude && event.venue?.longitude) {
    const lat = parseFloat(event.venue.latitude);
    const lng = parseFloat(event.venue.longitude);
    if (!isNaN(lat) && !isNaN(lng)) {
      return [lat, lng];
    }
  }
  return null;
}

// Calculate distance between two points in km using Haversine formula
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 3958.8; // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c; // Distance in miles
}

// Helper function to calculate appropriate zoom level for a given distance
function getZoomForDistance(miles: number): number {
  if (miles <= 1) return 15;
  if (miles <= 5) return 13;
  if (miles <= 10) return 12;
  if (miles <= 20) return 11;
  return 10;
}

// Helper function to calculate bounds for a radius
function calculateBoundsForRadius(center: [number, number], radiusMiles: number): L.LatLngBounds {
  const lat = center[0];
  const lng = center[1];
  
  const latDelta = radiusMiles / 69;
  const lngDelta = radiusMiles / (69 * Math.cos(lat * Math.PI / 180));
  
  const southWest = L.latLng(lat - latDelta, lng - lngDelta);
  const northEast = L.latLng(lat + latDelta, lng + lngDelta);
  
  return L.latLngBounds(southWest, northEast);
}

// Helper function to fit map bounds to include a radius
function fitBoundsForRadius(map: Map, center: [number, number], radiusMiles: number) {
  const bounds = calculateBoundsForRadius(center, radiusMiles);
  const zoomLevel = getZoomForDistance(radiusMiles);
  
  map.setZoom(zoomLevel);
  map.fitBounds(bounds, {
    padding: [20, 20] as PointTuple,
    maxZoom: zoomLevel,
    animate: true
  });
}

// Component to handle map updates and display radius circle
function MapUpdater({ 
  events, 
  selectedEvent, 
  center, 
  zoom,
  userLocation,
  distance,
  showRadius
}: { 
  events: Event[]; 
  selectedEvent: Event | null; 
  center?: [number, number]; 
  zoom?: number;
  userLocation?: {latitude: number; longitude: number} | null;
  distance: string;
  showRadius: boolean;
}) {
  const map = useMap();
  const defaultCenter: [number, number] = [32.2226, -110.9747];
  
  useEffect(() => {
    if (center && zoom) {
      map.setView(center, zoom);
    }
  }, [map, center, zoom]);
  
  const circleRef = useRef<L.Circle | null>(null);
  
  useEffect(() => {
    if (selectedEvent) {
      const coords = getEventCoordinates(selectedEvent);
      if (coords) {
        map.setView(coords, 15, { animate: true });
      }
    }
  }, [map, selectedEvent]);
  
  useEffect(() => {
    if (circleRef.current) {
      circleRef.current.remove();
      circleRef.current = null;
    }
    
    if (userLocation && showRadius) {
      const distanceInMeters = Number(distance) * 1609.34;
      circleRef.current = L.circle(
        [userLocation.latitude, userLocation.longitude], 
        {
          radius: distanceInMeters,
          color: '#3b82f6',
          fillColor: '#3b82f680',
          fillOpacity: 0.1,
          weight: 2
        }
      ).addTo(map);
      
      const center: [number, number] = [userLocation.latitude, userLocation.longitude];
      const radiusMiles = Number(distance);
      fitBoundsForRadius(map, center, radiusMiles);
    } else {
      map.setView(defaultCenter, 10, { animate: true });
    }
    
    return () => {
      if (circleRef.current) {
        circleRef.current.remove();
      }
    };
  }, [map, userLocation, distance, showRadius]);
  
  return null;
}

interface EventMapProps {
  events: Event[];
  selectedEvent?: Event | null;
  onEventSelect?: (event: Event | null) => void;
  center?: [number, number];
  zoom?: number;
  mapDistance?: string;
  userLocation?: [number, number] | null;
  onLocationSelect?: (location: [number, number]) => void;
  onReset?: () => void;
}

export default function EventMapInternal({ 
  events, 
  selectedEvent = null, 
  onEventSelect, 
  center,
  zoom: initialZoom,
  mapDistance,
  userLocation,
  onLocationSelect,
  onReset
}: EventMapProps) {
  const { getCurrentLocation } = useLocation();
  const defaultCenter: [number, number] = [32.2226, -110.9747];
  
  const baseZoom = mapDistance ? getZoomForDistance(Number(mapDistance)) : 12;
  const zoom = initialZoom || (center ? Math.max(baseZoom, 14) : baseZoom);
  const mapRef = useRef<Map | null>(null);

  // Simplified state management
  const [distance, setDistance] = useState(mapDistance || "10");
  const [locationAccessed, setLocationAccessed] = useState(false);
  const [locationModeActive, setLocationModeActive] = useState(false);
  const [userLocationState, setUserLocation] = useState<{latitude: number; longitude: number} | null>(null);
  const [filteredEvents, setFilteredEvents] = useState<Event[]>(events);
  const [searchInput, setSearchInput] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState(false);
  const [showRadius, setShowRadius] = useState(false);

  // Store references to markers for popup control
  const markerRefs = useRef<{ [key: string]: L.Marker | null }>({});
  const prevSelectedEventRef = useRef<Event | null>(null);

  // Cleanup map on unmount
  useEffect(() => {
    return () => {
      if (mapRef.current) {
        try {
          mapRef.current.remove();
          mapRef.current = null;
        } catch (error) {
          console.warn('Event map cleanup warning:', error);
        }
      }
    };
  }, []);

  // Handle location mode properly
  useEffect(() => {
    if (userLocation && mapDistance) {
      setShowRadius(true);
      setLocationModeActive(true);
      setUserLocation({ latitude: userLocation[0], longitude: userLocation[1] });
      setDistance(mapDistance);
    }
  }, [userLocation, mapDistance]);

  // Store map reference when component mounts
  const onMapLoad = useCallback((map: Map) => {
    mapRef.current = map;
  }, []);

  // Update map when distance or location changes
  useEffect(() => {
    if (!mapRef.current || !userLocationState || !locationModeActive) return;
    
    const map = mapRef.current;
    const radiusMiles = Number(distance);
    const center: [number, number] = [userLocationState.latitude, userLocationState.longitude];
    
    fitBoundsForRadius(map, center, radiusMiles);
    
    if (events.length > 0) {
      const filtered = events.filter(event => {
        const coords = getEventCoordinates(event);
        if (!coords) return false;
        
        const eventDistance = calculateDistance(
          userLocationState.latitude, 
          userLocationState.longitude, 
          coords[0], 
          coords[1]
        );
        
        return eventDistance <= radiusMiles;
      });
      
      setFilteredEvents(filtered);
    }
  }, [events, distance, userLocationState, locationModeActive]);

  // Add custom CSS for the selected marker
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      .selected-marker {
        filter: hue-rotate(120deg) brightness(1.2) drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
        z-index: 1000 !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  // Handle marker click with toggle functionality
  const handleMarkerClick = useCallback((event: Event) => {
    if (selectedEvent?.id === event.id) {
      onEventSelect?.(null);
    } else {
      onEventSelect?.(event);
    }
  }, [selectedEvent, onEventSelect]);

  // Handle popup management when selected event changes
  useEffect(() => {
    if (prevSelectedEventRef.current && prevSelectedEventRef.current.id !== selectedEvent?.id) {
      const prevMarker = markerRefs.current[prevSelectedEventRef.current.id];
      if (prevMarker) {
        prevMarker.closePopup();
      }
    }

    if (selectedEvent) {
      const marker = markerRefs.current[selectedEvent.id];
      if (marker) {
        setTimeout(() => {
          marker.openPopup();
        }, 300);
      }
    }

    prevSelectedEventRef.current = selectedEvent;
  }, [selectedEvent]);

  // Close all popups when deselecting
  useEffect(() => {
    if (!selectedEvent && prevSelectedEventRef.current && mapRef.current) {
      mapRef.current.closePopup();
    }
  }, [selectedEvent]);

  // Sync userLocation prop with internal state
  useEffect(() => {
    if (userLocation) {
      setUserLocation({ latitude: userLocation[0], longitude: userLocation[1] });
      setLocationModeActive(true);
    }
  }, [userLocation]);

  // Determine which events to display on the map
  const eventsToDisplay = selectedEvent 
    ? [selectedEvent] 
    : (locationModeActive && userLocationState ? filteredEvents : events);

  const handleLocationToggle = async () => {
    if (locationModeActive) {
      setLocationModeActive(false);
      setUserLocation(null);
      setLocationAccessed(false);
      setShowRadius(false);
      setDistance('10');
      
      if (mapRef.current) {
        mapRef.current.eachLayer((layer) => {
          if (layer instanceof L.Circle) {
            mapRef.current?.removeLayer(layer);
          }
        });
        
        mapRef.current.setView(defaultCenter, 11, { animate: true });
        
        if (events.length > 0) {
          const validEvents = events.filter(event => getEventCoordinates(event) !== null);
          
          if (validEvents.length > 0) {
            const bounds = L.latLngBounds(
              validEvents.map(event => {
                const coords = getEventCoordinates(event);
                return coords ? L.latLng(coords[0], coords[1]) : null;
              }).filter(Boolean) as L.LatLng[]
            );
            
            if (bounds.isValid()) {
              setTimeout(() => {
                if (mapRef.current) {
                  mapRef.current.fitBounds(bounds, {
                    padding: [50, 50] as PointTuple,
                    maxZoom: 11,
                    animate: true
                  });
                }
              }, 100);
            }
          }
        }
      }
      
      setFilteredEvents(events);
      onEventSelect?.(null);
      // Don't call onLocationSelect with [0,0] - let the map stay at defaultCenter
      
      return;
    }

    try {
      const location = await getCurrentLocation();
      
      if (!location) {
        console.warn('No location retrieved');
        return;
      }
      
      if (mapRef.current) {
        const radiusMiles = Number(distance);
        const center: [number, number] = [location.latitude, location.longitude];
        
        fitBoundsForRadius(mapRef.current, center, radiusMiles);
      }
      
      setUserLocation(location);
      onLocationSelect?.([location.latitude, location.longitude]);
      setLocationAccessed(true);
      setLocationModeActive(true);
      
      if (events.length > 0) {
        const maxDistance = Number(distance);
        const filtered = events.filter(event => {
          const coords = getEventCoordinates(event);
          if (!coords) return false;
          
          const eventDistance = calculateDistance(
            location.latitude, 
            location.longitude, 
            coords[0], 
            coords[1]
          );
          
          return eventDistance <= maxDistance;
        });
        
        setFilteredEvents(filtered);
      }
    } catch (error) {
      console.error("Location selection error:", error);
    }
  };

  // Handle location search
  const handleLocationSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchInput.trim()) return;
    
    setIsSearching(true);
    setSearchError(false);
    
    try {
      let searchQuery = searchInput.trim();
      
      if (searchQuery.toLowerCase() === "u of a" || 
          searchQuery.toLowerCase() === "uofa") {
        searchQuery = "University of Arizona, Tucson, AZ";
      } else if (!searchQuery.toLowerCase().includes("arizona") && 
          !searchQuery.toLowerCase().includes("az")) {
        searchQuery = `${searchQuery}, Arizona`;
      }
      
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?` + 
        `format=json&` +
        `q=${encodeURIComponent(searchQuery)}&` +
        `countrycodes=us&` +
        `limit=1`
      );
      
      const data = await response.json();
      
      if (data && data.length > 0) {
        const location = {
          latitude: parseFloat(data[0].lat),
          longitude: parseFloat(data[0].lon)
        };
        
        const isInArizona = 
          location.latitude >= 31.3322 && location.latitude <= 37.0043 &&
          location.longitude >= -114.8183 && location.longitude <= -109.0452;
        
        if (!isInArizona) {
          console.error("Location not in Arizona");
          setSearchError(true);
          setTimeout(() => setSearchError(false), 3000);
          return;
        }
        
        if (mapRef.current) {
          const radiusMiles = Number(distance);
          const center: [number, number] = [location.latitude, location.longitude];
          
          fitBoundsForRadius(mapRef.current, center, radiusMiles);
        }
        
        setUserLocation(location);
        onLocationSelect?.([location.latitude, location.longitude]);
        setLocationAccessed(true);
        setLocationModeActive(true);
        
        if (events.length > 0) {
          const maxDistance = Number(distance);
          const filtered = events.filter(event => {
            const coords = getEventCoordinates(event);
            if (!coords) return false;
            
            const eventDistance = calculateDistance(
              location.latitude, 
              location.longitude, 
              coords[0], 
              coords[1]
            );
            
            return eventDistance <= maxDistance;
          });
          
          setFilteredEvents(filtered);
        }
      } else {
        console.error("Location not found in Arizona");
        setSearchError(true);
        setTimeout(() => setSearchError(false), 3000);
      }
    } catch (error) {
      console.error("Error searching location:", error);
      setSearchError(true);
      setTimeout(() => setSearchError(false), 3000);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle map distance change
  const handleDistanceChange = (value: string) => {
    setDistance(value);
    
    if (mapRef.current && userLocationState && locationModeActive) {
      const radiusMiles = Number(value);
      const center: [number, number] = [userLocationState.latitude, userLocationState.longitude];
      
      const zoomLevel = getZoomForDistance(radiusMiles);
      mapRef.current.setZoom(zoomLevel);
      
      const bounds = calculateBoundsForRadius(center, radiusMiles);
      mapRef.current.fitBounds(bounds, {
        padding: [20, 20] as PointTuple,
        maxZoom: zoomLevel,
        animate: true
      });
      
      if (events.length > 0) {
        const filtered = events.filter(event => {
          const coords = getEventCoordinates(event);
          if (!coords) return false;
          
          const eventDistance = calculateDistance(
            userLocationState.latitude, 
            userLocationState.longitude, 
            coords[0], 
            coords[1]
          );
          
          return eventDistance <= radiusMiles;
        });
        
        setFilteredEvents(filtered);
      }
    }
    
    if (onLocationSelect && userLocationState) {
      onLocationSelect([userLocationState.latitude, userLocationState.longitude]);
    }
  };

  return (
    <div className="w-full h-full rounded-lg overflow-hidden flex flex-col max-h-full min-h-0">
      {/* Map Controls */}
      <div className="space-y-2 mb-2 z-50 relative flex-shrink-0">
        <Button
          onClick={handleLocationToggle}
          className={cn(
            "w-full",
            locationModeActive ? "bg-primary hover:bg-primary/90" : ""
          )}
          variant={locationModeActive ? "default" : "outline"}
        >
          <MapPin className="mr-2 h-4 w-4" />
          {locationModeActive ? "Reset Map View" : "Use My Location"}
        </Button>
        
        <MapDistanceSelect 
          defaultValue={distance} 
          onChange={handleDistanceChange}
          label=""
          className="mt-2"
          disabled={!locationAccessed || !locationModeActive}
        />
        
        <form onSubmit={handleLocationSearch} className="flex w-full gap-1">
          <Input
            type="text"
            placeholder="Search in Arizona..."
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            className="h-9 text-sm"
          />
          <Button 
            type="submit" 
            size="sm" 
            variant={searchError ? "destructive" : "outline"}
            className="px-2" 
            disabled={isSearching || !searchInput.trim()}
          >
            {isSearching ? (
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            ) : (
              <Search className="h-4 w-4" />
            )}
          </Button>
        </form>
      </div>

      {/* Map Container */}
      <div className="flex-1 min-h-[300px] max-h-full relative overflow-hidden">
        <MapContainer 
          center={center || defaultCenter} 
          zoom={10} 
          className="h-full w-full z-0"
          ref={(mapInstance) => {
            if (mapInstance) {
              mapRef.current = mapInstance;
            }
          }}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          <MapUpdater 
            events={events}
            selectedEvent={selectedEvent}
            center={center}
            zoom={10}
            userLocation={userLocationState}
            distance={mapDistance || "10"}
            showRadius={!!userLocationState}
          />
          
          {eventsToDisplay.map((event) => {
            const coords = getEventCoordinates(event);
            return coords ? (
              <Marker
                key={event.id}
                position={coords}
                icon={selectedEvent?.id === event.id ? selectedIcon : icon}
                ref={(ref) => {
                  if (ref) {
                    markerRefs.current[event.id] = ref;
                  }
                }}
                eventHandlers={{
                  click: () => handleMarkerClick(event),
                }}
              >
                <Popup>
                  <div className="text-sm leading-tight">
                    <h3 className="font-bold mb-1">{event.name}</h3>
                    <div className="space-y-0">
                      <div>{format(new Date(event.startDateTime), 'MMMM d, yyyy h:mm a')}</div>
                      {event.venue && <div>{event.venue.name}</div>}
                    </div>
                  </div>
                </Popup>
              </Marker>
            ) : null;
          })}
        </MapContainer>
      </div>
    </div>
  );
}
