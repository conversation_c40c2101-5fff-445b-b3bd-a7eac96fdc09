"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_components_map_event-map-manual_tsx",{

/***/ "(app-pages-browser)/./components/map/event-map-manual.tsx":
/*!*********************************************!*\
  !*** ./components/map/event-map-manual.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EventMapManual)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! leaflet */ \"(app-pages-browser)/./node_modules/leaflet/dist/leaflet-src.js\");\n/* harmony import */ var leaflet__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(leaflet__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var leaflet_dist_leaflet_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! leaflet/dist/leaflet.css */ \"(app-pages-browser)/./node_modules/leaflet/dist/leaflet.css\");\n/* harmony import */ var _styles_map_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/map.css */ \"(app-pages-browser)/./styles/map.css\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_location__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-location */ \"(app-pages-browser)/./hooks/use-location.ts\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_MapPin_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MapPin,Search!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _components_map_map_distance_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/map/map-distance-select */ \"(app-pages-browser)/./components/map/map-distance-select.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Utility function to get event coordinates\nfunction getEventCoordinates(event) {\n    var _event_venue, _event_venue1;\n    if (((_event_venue = event.venue) === null || _event_venue === void 0 ? void 0 : _event_venue.latitude) && ((_event_venue1 = event.venue) === null || _event_venue1 === void 0 ? void 0 : _event_venue1.longitude)) {\n        const lat = parseFloat(event.venue.latitude);\n        const lng = parseFloat(event.venue.longitude);\n        if (!isNaN(lat) && !isNaN(lng)) {\n            return [\n                lat,\n                lng\n            ];\n        }\n    }\n    return null;\n}\n// Calculate distance between two points using Haversine formula\nfunction calculateDistance(lat1, lon1, lat2, lon2) {\n    const R = 3958.8; // Earth's radius in miles\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLon = (lon2 - lon1) * Math.PI / 180;\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c; // Distance in miles\n}\n// Helper function to calculate appropriate zoom level for a given distance\nfunction getZoomForDistance(miles) {\n    if (miles <= 1) return 15;\n    if (miles <= 5) return 13;\n    if (miles <= 10) return 12;\n    if (miles <= 20) return 11;\n    return 10;\n}\nfunction EventMapManual(param) {\n    let { events, selectedEvent = null, onEventSelect, center, zoom: initialZoom, mapDistance, userLocation, onLocationSelect, onReset } = param;\n    _s();\n    const { getCurrentLocation } = (0,_hooks_use_location__WEBPACK_IMPORTED_MODULE_6__.useLocation)();\n    const defaultCenter = [\n        32.2226,\n        -110.9747\n    ];\n    const mapRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const markersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const circleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isMapReady, setIsMapReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State management\n    const [distance, setDistance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(mapDistance || \"10\");\n    const [locationAccessed, setLocationAccessed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [locationModeActive, setLocationModeActive] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userLocationState, setUserLocation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filteredEvents, setFilteredEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(events);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isSearching, setIsSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchError, setSearchError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Initialize map manually\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMapManual.useEffect\": ()=>{\n            if (!containerRef.current || mapRef.current) return;\n            try {\n                const map = leaflet__WEBPACK_IMPORTED_MODULE_2___default().map(containerRef.current, {\n                    center: center || defaultCenter,\n                    zoom: 10,\n                    zoomControl: true,\n                    attributionControl: true\n                });\n                leaflet__WEBPACK_IMPORTED_MODULE_2___default().tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\n                    attribution: '&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n                }).addTo(map);\n                mapRef.current = map;\n                setIsMapReady(true);\n                console.log('Manual event map created successfully');\n            } catch (error) {\n                console.error('Failed to create manual event map:', error);\n            }\n        }\n    }[\"EventMapManual.useEffect\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMapManual.useEffect\": ()=>{\n            return ({\n                \"EventMapManual.useEffect\": ()=>{\n                    if (mapRef.current) {\n                        try {\n                            mapRef.current.remove();\n                            mapRef.current = null;\n                            console.log('Manual event map cleaned up');\n                        } catch (error) {\n                            console.warn('Event map cleanup error:', error);\n                        }\n                    }\n                }\n            })[\"EventMapManual.useEffect\"];\n        }\n    }[\"EventMapManual.useEffect\"], []);\n    // Handle user location\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMapManual.useEffect\": ()=>{\n            if (userLocation && mapDistance) {\n                setUserLocation({\n                    latitude: userLocation[0],\n                    longitude: userLocation[1]\n                });\n                setLocationModeActive(true);\n                setDistance(mapDistance);\n            }\n        }\n    }[\"EventMapManual.useEffect\"], [\n        userLocation,\n        mapDistance\n    ]);\n    // Handle events and markers\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMapManual.useEffect\": ()=>{\n            if (!mapRef.current || !isMapReady) return;\n            const map = mapRef.current;\n            // Clear existing markers\n            markersRef.current.forEach({\n                \"EventMapManual.useEffect\": (marker)=>{\n                    map.removeLayer(marker);\n                }\n            }[\"EventMapManual.useEffect\"]);\n            markersRef.current = [];\n            // Create icons\n            const icon = leaflet__WEBPACK_IMPORTED_MODULE_2___default().icon({\n                iconUrl: \"https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png\",\n                iconRetinaUrl: \"https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png\",\n                shadowUrl: \"https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png\",\n                iconSize: [\n                    25,\n                    41\n                ],\n                iconAnchor: [\n                    12,\n                    41\n                ],\n                popupAnchor: [\n                    1,\n                    -34\n                ],\n                shadowSize: [\n                    41,\n                    41\n                ]\n            });\n            const selectedIcon = leaflet__WEBPACK_IMPORTED_MODULE_2___default().icon({\n                iconUrl: \"https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png\",\n                iconRetinaUrl: \"https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png\",\n                shadowUrl: \"https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png\",\n                iconSize: [\n                    30,\n                    46\n                ],\n                iconAnchor: [\n                    15,\n                    46\n                ],\n                popupAnchor: [\n                    1,\n                    -34\n                ],\n                shadowSize: [\n                    41,\n                    41\n                ],\n                className: \"selected-marker\"\n            });\n            // Determine which events to display\n            const eventsToDisplay = selectedEvent ? [\n                selectedEvent\n            ] : locationModeActive && userLocationState ? filteredEvents : events;\n            // Add event markers\n            eventsToDisplay.forEach({\n                \"EventMapManual.useEffect\": (event)=>{\n                    const coords = getEventCoordinates(event);\n                    if (coords) {\n                        const marker = leaflet__WEBPACK_IMPORTED_MODULE_2___default().marker(coords, {\n                            icon: (selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) === event.id ? selectedIcon : icon\n                        }).addTo(map);\n                        const popupContent = '\\n          <div class=\"text-sm leading-tight\">\\n            <h3 class=\"font-bold mb-1\">'.concat(event.name, '</h3>\\n            <div class=\"space-y-0\">\\n              <div>').concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_10__.format)(new Date(event.startDateTime), 'MMMM d, yyyy h:mm a'), \"</div>\\n              \").concat(event.venue ? \"<div>\".concat(event.venue.name, \"</div>\") : '', \"\\n            </div>\\n          </div>\\n        \");\n                        marker.bindPopup(popupContent);\n                        marker.on('click', {\n                            \"EventMapManual.useEffect\": ()=>{\n                                if ((selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id) === event.id) {\n                                    onEventSelect === null || onEventSelect === void 0 ? void 0 : onEventSelect(null);\n                                } else {\n                                    onEventSelect === null || onEventSelect === void 0 ? void 0 : onEventSelect(event);\n                                }\n                            }\n                        }[\"EventMapManual.useEffect\"]);\n                        markersRef.current.push(marker);\n                    }\n                }\n            }[\"EventMapManual.useEffect\"]);\n            // Center map on selected event only\n            if (selectedEvent) {\n                const coords = getEventCoordinates(selectedEvent);\n                if (coords) {\n                    map.setView(coords, 15, {\n                        animate: true\n                    });\n                }\n            }\n        // Don't auto-fit bounds for all events - let the map stay at default center\n        }\n    }[\"EventMapManual.useEffect\"], [\n        events,\n        selectedEvent,\n        filteredEvents,\n        locationModeActive,\n        userLocationState,\n        isMapReady\n    ]);\n    // Handle radius circle\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMapManual.useEffect\": ()=>{\n            if (!mapRef.current || !isMapReady) return;\n            const map = mapRef.current;\n            // Remove existing circle\n            if (circleRef.current) {\n                map.removeLayer(circleRef.current);\n                circleRef.current = null;\n            }\n            // Add radius circle if user location is active\n            if (userLocationState && locationModeActive) {\n                const circle = leaflet__WEBPACK_IMPORTED_MODULE_2___default().circle([\n                    userLocationState.latitude,\n                    userLocationState.longitude\n                ], {\n                    radius: Number(distance) * 1609.34,\n                    color: '#3b82f6',\n                    fillColor: '#3b82f680',\n                    fillOpacity: 0.1,\n                    weight: 2\n                }).addTo(map);\n                circleRef.current = circle;\n                // Center on user location with appropriate zoom\n                const zoomLevel = getZoomForDistance(Number(distance));\n                map.setView([\n                    userLocationState.latitude,\n                    userLocationState.longitude\n                ], zoomLevel, {\n                    animate: true\n                });\n            }\n        }\n    }[\"EventMapManual.useEffect\"], [\n        userLocationState,\n        distance,\n        locationModeActive,\n        isMapReady\n    ]);\n    // Filter events based on distance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMapManual.useEffect\": ()=>{\n            if (!userLocationState || !locationModeActive) {\n                setFilteredEvents(events);\n                return;\n            }\n            const maxDistance = Number(distance);\n            const filtered = events.filter({\n                \"EventMapManual.useEffect.filtered\": (event)=>{\n                    const coords = getEventCoordinates(event);\n                    if (!coords) return false;\n                    const eventDistance = calculateDistance(userLocationState.latitude, userLocationState.longitude, coords[0], coords[1]);\n                    return eventDistance <= maxDistance;\n                }\n            }[\"EventMapManual.useEffect.filtered\"]);\n            setFilteredEvents(filtered);\n        }\n    }[\"EventMapManual.useEffect\"], [\n        events,\n        userLocationState,\n        distance,\n        locationModeActive\n    ]);\n    const handleLocationToggle = async ()=>{\n        if (locationModeActive) {\n            // Reset location mode\n            setLocationModeActive(false);\n            setUserLocation(null);\n            setLocationAccessed(false);\n            setDistance('10');\n            if (mapRef.current) {\n                // Remove circle\n                if (circleRef.current) {\n                    mapRef.current.removeLayer(circleRef.current);\n                    circleRef.current = null;\n                }\n                // Reset to default view\n                mapRef.current.setView(defaultCenter, 11, {\n                    animate: true\n                });\n            }\n            setFilteredEvents(events);\n            onEventSelect === null || onEventSelect === void 0 ? void 0 : onEventSelect(null);\n            // Don't call onLocationSelect with [0,0] - let the map stay at defaultCenter\n            return;\n        }\n        try {\n            const location = await getCurrentLocation();\n            if (!location) {\n                console.warn('No location retrieved');\n                return;\n            }\n            setUserLocation(location);\n            onLocationSelect === null || onLocationSelect === void 0 ? void 0 : onLocationSelect([\n                location.latitude,\n                location.longitude\n            ]);\n            setLocationAccessed(true);\n            setLocationModeActive(true);\n        } catch (error) {\n            console.error(\"Location selection error:\", error);\n        }\n    };\n    const handleLocationSearch = async (e)=>{\n        e.preventDefault();\n        if (!searchInput.trim()) return;\n        setIsSearching(true);\n        setSearchError(false);\n        try {\n            let searchQuery = searchInput.trim();\n            if (searchQuery.toLowerCase() === \"u of a\" || searchQuery.toLowerCase() === \"uofa\") {\n                searchQuery = \"University of Arizona, Tucson, AZ\";\n            } else if (!searchQuery.toLowerCase().includes(\"arizona\") && !searchQuery.toLowerCase().includes(\"az\")) {\n                searchQuery = \"\".concat(searchQuery, \", Arizona\");\n            }\n            const response = await fetch(\"https://nominatim.openstreetmap.org/search?\" + \"format=json&\" + \"q=\".concat(encodeURIComponent(searchQuery), \"&\") + \"countrycodes=us&\" + \"limit=1\");\n            const data = await response.json();\n            if (data && data.length > 0) {\n                const location = {\n                    latitude: parseFloat(data[0].lat),\n                    longitude: parseFloat(data[0].lon)\n                };\n                const isInArizona = location.latitude >= 31.3322 && location.latitude <= 37.0043 && location.longitude >= -114.8183 && location.longitude <= -109.0452;\n                if (!isInArizona) {\n                    setSearchError(true);\n                    setTimeout(()=>setSearchError(false), 3000);\n                    return;\n                }\n                setUserLocation(location);\n                onLocationSelect === null || onLocationSelect === void 0 ? void 0 : onLocationSelect([\n                    location.latitude,\n                    location.longitude\n                ]);\n                setLocationAccessed(true);\n                setLocationModeActive(true);\n            } else {\n                setSearchError(true);\n                setTimeout(()=>setSearchError(false), 3000);\n            }\n        } catch (error) {\n            console.error(\"Error searching location:\", error);\n            setSearchError(true);\n            setTimeout(()=>setSearchError(false), 3000);\n        } finally{\n            setIsSearching(false);\n        }\n    };\n    const handleDistanceChange = (value)=>{\n        setDistance(value);\n        if (onLocationSelect && userLocationState) {\n            onLocationSelect([\n                userLocationState.latitude,\n                userLocationState.longitude\n            ]);\n        }\n    };\n    // Add custom CSS for selected marker\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EventMapManual.useEffect\": ()=>{\n            const style = document.createElement('style');\n            style.innerHTML = \"\\n      .selected-marker {\\n        filter: hue-rotate(120deg) brightness(1.2) drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));\\n        z-index: 1000 !important;\\n      }\\n    \";\n            document.head.appendChild(style);\n            return ({\n                \"EventMapManual.useEffect\": ()=>{\n                    document.head.removeChild(style);\n                }\n            })[\"EventMapManual.useEffect\"];\n        }\n    }[\"EventMapManual.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full rounded-lg overflow-hidden flex flex-col max-h-full min-h-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2 mb-2 z-50 relative flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleLocationToggle,\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"w-full\", locationModeActive ? \"bg-primary hover:bg-primary/90\" : \"\"),\n                        variant: locationModeActive ? \"default\" : \"outline\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this),\n                            locationModeActive ? \"Reset Map View\" : \"Use My Location\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_map_map_distance_select__WEBPACK_IMPORTED_MODULE_7__.MapDistanceSelect, {\n                        defaultValue: distance,\n                        onChange: handleDistanceChange,\n                        label: \"\",\n                        className: \"mt-2\",\n                        disabled: !locationAccessed || !locationModeActive\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleLocationSearch,\n                        className: \"flex w-full gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_8__.Input, {\n                                type: \"text\",\n                                placeholder: \"Search in Arizona...\",\n                                value: searchInput,\n                                onChange: (e)=>setSearchInput(e.target.value),\n                                className: \"h-9 text-sm\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                type: \"submit\",\n                                size: \"sm\",\n                                variant: searchError ? \"destructive\" : \"outline\",\n                                className: \"px-2\",\n                                disabled: isSearching || !searchInput.trim(),\n                                children: isSearching ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MapPin_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 min-h-[300px] max-h-full relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: containerRef,\n                        className: \"h-full w-full z-0\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, this),\n                    !isMapReady && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center bg-background/80\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Loading map...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n                lineNumber: 461,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\PROJECTS\\\\tucsonlovesmusic.com\\\\Prototype13\\\\frontend\\\\components\\\\map\\\\event-map-manual.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, this);\n}\n_s(EventMapManual, \"x+JML0T9jHIHB2+j2z9qMtIQHzU=\", false, function() {\n    return [\n        _hooks_use_location__WEBPACK_IMPORTED_MODULE_6__.useLocation\n    ];\n});\n_c = EventMapManual;\nvar _c;\n$RefreshReg$(_c, \"EventMapManual\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/map/event-map-manual.tsx\n"));

/***/ })

});